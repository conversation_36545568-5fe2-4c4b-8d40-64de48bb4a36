// 管理员相关类型定义

import { BaseEntity } from './common';

export interface AdminUser extends BaseEntity {
  username: string;
  email: string;
  fullName: string;
  phone?: string;
  role: 'ADMIN' | 'USER';
  avatarUrl?: string;
  isActive: boolean;
  emailVerified: boolean;
  lastLoginAt?: string;
  loginCount: number;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    token: string;
    user: AdminUser;
    permissions?: string[];
  };
  timestamp: string;
}

export interface DashboardStats {
  users: {
    total: number;
    todayNew: number;
    activeUsers: number;
    adminCount: number;
  };
  hotels: {
    total: number;
    active: number;
    avgRating: number;
    todayNew: number;
  };
  bookings: {
    total: number;
    todayNew: number;
    totalRevenue: number;
    todayRevenue: number;
    statusDistribution: {
      name: string;
      value: number;
    }[];
  };
  reviews: {
    total: number;
    pending: number;
    avgRating: number;
    ratingDistribution: {
      rating: string;
      count: number;
    }[];
  };
}

export interface TrendData {
  labels: string[];
  bookings?: number[];
  revenue?: number[];
  users?: number[];
  reviews?: number[];
}

export interface Hotel extends BaseEntity {
  name: string;
  description: string;
  address: string;
  city: string;
  province: string;
  postalCode: string;
  latitude: number;
  longitude: number;
  starRating: number;
  phone: string;
  email: string;
  checkInTime: string;
  checkOutTime: string;
  amenities: string[];
  images: string[];
  isActive: boolean;
  totalRooms: number;
  availableRooms: number;
  occupancyRate: number;
  totalBookings: number;
  avgRating: number;
  createdBy?: number;
  updatedBy?: number;
}

export interface Room extends BaseEntity {
  hotelId: number;
  roomTypeId: number;
  roomNumber: string;
  floorNumber: number;
  status: 'AVAILABLE' | 'OCCUPIED' | 'MAINTENANCE' | 'OUT_OF_ORDER';
  hotel?: Hotel;
  roomType?: RoomType;
}

// 房型配置接口
export interface RoomTypeConfig {
  name: string;
  description?: string;
  basePrice: number;
  maxOccupancy: number;
  bedType?: string;
  roomSize?: number;
  amenities?: string[];
  images?: string[];
}

// 房间预览接口
export interface RoomPreview {
  roomNumber: string;
  floorNumber: number;
  roomPosition: number;
  roomType: 'STANDARD' | 'DELUXE';
  selected: boolean;
}

// 酒店创建请求接口
export interface HotelCreationRequest {
  // 基本信息
  name: string;
  description?: string;
  address: string;
  city: string;
  province?: string;
  postalCode?: string;
  latitude?: number;
  longitude?: number;
  starRating: number;
  phone?: string;
  email?: string;
  website?: string;
  checkInTime?: string;
  checkOutTime?: string;
  amenities?: string[];
  images?: string[];
  policies?: Record<string, any>;

  // 楼层配置
  totalFloors: number;
  roomsPerFloor: number;

  // 房型配置
  standardRoomConfig?: RoomTypeConfig;
  deluxeRoomConfig?: RoomTypeConfig;

  // 豪华间房间号列表
  deluxeRoomNumbers?: string[];
}

export interface RoomType extends BaseEntity {
  hotelId: number;
  name: string;
  description: string;
  basePrice: number;
  maxOccupancy: number;
  roomSize: number;
  bedType: string;
  amenities: string[];
  images: string[];
  isActive: boolean;
}

export interface Booking extends BaseEntity {
  bookingNumber: string;
  userId: number;
  hotelId: number;
  roomId: number;
  checkInDate: string;
  checkOutDate: string;
  totalAmount: number;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
  paymentStatus: 'PENDING' | 'PAID' | 'FAILED' | 'REFUNDED';
  adminNotes?: string;
  cancellationReason?: string;
  cancelledBy?: number;
  refundAmount: number;
  refundStatus: 'NONE' | 'PENDING' | 'PROCESSED' | 'FAILED';
  source: 'WEB' | 'MOBILE' | 'ADMIN' | 'API';
  user?: AdminUser;
  hotel?: Hotel;
  room?: Room;
}

export interface Review extends BaseEntity {
  userId: number;
  hotelId: number;
  bookingId: number;
  overallRating: number;
  serviceRating: number;
  cleanlinessRating: number;
  locationRating: number;
  valueRating: number;
  comment: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'HIDDEN';
  adminReply?: string;
  adminReplyBy?: number;
  adminReplyAt?: string;
  approvedBy?: number;
  approvedAt?: string;
  rejectionReason?: string;
  isFeatured: boolean;
  helpfulCount: number;
  user?: AdminUser;
  hotel?: Hotel;
}

export interface CulturalPackage extends BaseEntity {
  name: string;
  description: string;
  price: number;
  duration: number;
  maxParticipants: number;
  category: string;
  difficulty: 'EASY' | 'MODERATE' | 'HARD';
  includes: string[];
  excludes: string[];
  itinerary: string[];
  images: string[];
  culturalSignificance: string;
  bestSeason: string;
  languageRequirements: string;
  physicalRequirements: string;
  bookingNotice: string;
  isActive: boolean;
  isPopular: boolean;
  isRecommended: boolean;
}

export interface SystemSettings {
  siteName: string;
  siteDescription: string;
  defaultCurrency: string;
  bookingAdvanceDays: number;
  cancellationHours: number;
  taxRate: number;
  adminEmail: string;
  maintenanceMode: boolean;
  maintenanceMessage: string;
  registrationEnabled: boolean;
  emailNotificationEnabled: boolean;
  smsNotificationEnabled: boolean;
}

export interface AuditLog extends BaseEntity {
  userId: number;
  username: string;
  action: string;
  resourceType: string;
  resourceId?: number;
  description: string;
  oldValues?: Record<string, any>;
  newValues?: Record<string, any>;
  ipAddress: string;
  userAgent: string;
}

export interface AdminSession extends BaseEntity {
  userId: number;
  sessionToken: string;
  ipAddress: string;
  userAgent: string;
  lastActivity: string;
  expiresAt: string;
  isActive: boolean;
}

export interface Review extends BaseEntity {
  userId: number;
  hotelId: number;
  bookingId: number;
  rating: number;
  comment: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'HIDDEN';
  adminReply?: string;
  user?: AdminUser;
  hotel?: Hotel;
}

// 报表相关类型定义
export interface ReportFilters {
  dateRange?: [string, string];
  hotelId?: string;
  status?: string;
  userId?: string;
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface BookingReportItem {
  id: string;
  bookingNumber: string;
  hotelName: string;
  roomType: string;
  guestName: string;
  checkInDate: string;
  checkOutDate: string;
  nights: number;
  totalAmount: number;
  status: string;
  createdAt: string;
  paymentStatus: string;
}

export interface RevenueReportItem {
  date: string;
  hotelName: string;
  bookingCount: number;
  totalRevenue: number;
  averageOrderValue: number;
  occupancyRate: number;
}

export interface UserReportItem {
  id: string;
  username: string;
  email: string;
  fullName: string;
  registrationDate: string;
  lastLoginDate: string;
  bookingCount: number;
  totalSpent: number;
  status: string;
}

export interface ReviewReportItem {
  id: string;
  hotelName: string;
  guestName: string;
  rating: number;
  comment: string;
  createdAt: string;
  status: string;
  adminReply?: string;
}

export interface ReportResponse<T> {
  success: boolean;
  message: string;
  data: {
    items: T[];
    total: number;
    page: number;
    pageSize: number;
    summary?: {
      totalRevenue?: number;
      totalBookings?: number;
      averageRating?: number;
      [key: string]: any;
    };
  };
  timestamp: string;
}

export type ReportType = 'bookings' | 'revenue' | 'users' | 'reviews';

export interface ExportOptions {
  format: 'csv' | 'excel';
  filename?: string;
  filters?: ReportFilters;
}
